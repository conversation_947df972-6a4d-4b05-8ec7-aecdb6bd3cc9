#!/usr/bin/env python3
"""
GPT-2 Model Quantization Demo using Quanto
This script demonstrates quantizing GPT-2 model and comparing memory usage and inference performance.
"""

import torch
import psutil
import os
import time
from transformers import GPT2LMHeadModel, GPT2Tokenizer
from quanto import quantize, freeze, qint8, qint4
import gc


def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024


def get_model_size(model):
    """Calculate model size in MB"""
    param_size = 0
    buffer_size = 0
    
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    model_size = (param_size + buffer_size) / 1024 / 1024
    return model_size


def generate_text(model, tokenizer, input_text, max_length=50, device='cpu'):
    """Generate text using the model"""
    # Tokenize input
    input_ids = tokenizer.encode(input_text, return_tensors='pt').to(device)
    
    # Generate
    with torch.no_grad():
        start_time = time.time()
        output = model.generate(
            input_ids,
            max_length=max_length,
            num_return_sequences=1,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
        end_time = time.time()
    
    # Decode output
    generated_text = tokenizer.decode(output[0], skip_special_tokens=True)
    generation_time = end_time - start_time
    
    return generated_text, generation_time


def main():
    print("=== GPT-2 Quantization Demo with Quanto ===\n")
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load tokenizer and model
    print("Loading GPT-2 model and tokenizer...")
    tokenizer = GPT2Tokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token
    
    # Original model
    model_original = GPT2LMHeadModel.from_pretrained('gpt2')
    model_original.to(device)
    model_original.eval()
    
    # Measure original model
    print("\n=== Original Model ===")
    original_model_size = get_model_size(model_original)
    print(f"Original model size: {original_model_size:.2f} MB")
    
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    memory_before_original = get_memory_usage()
    print(f"Memory usage with original model: {memory_before_original:.2f} MB")
    
    # Test original model inference
    test_input = "The future of artificial intelligence is"
    print(f"\nInput: '{test_input}'")
    
    original_output, original_time = generate_text(
        model_original, tokenizer, test_input, max_length=50, device=device
    )
    print(f"Original model output: {original_output}")
    print(f"Original model generation time: {original_time:.3f} seconds")
    
    # Create quantized model (INT8)
    print("\n=== Quantizing Model to INT8 ===")
    model_int8 = GPT2LMHeadModel.from_pretrained('gpt2')
    
    # Quantize to INT8
    quantize(model_int8, weights=qint8, activations=qint8)
    freeze(model_int8)
    
    model_int8.to(device)
    model_int8.eval()
    
    # Measure INT8 model
    int8_model_size = get_model_size(model_int8)
    print(f"INT8 quantized model size: {int8_model_size:.2f} MB")
    print(f"Size reduction: {((original_model_size - int8_model_size) / original_model_size * 100):.1f}%")
    
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    memory_with_int8 = get_memory_usage()
    print(f"Memory usage with INT8 model: {memory_with_int8:.2f} MB")
    
    # Test INT8 model inference
    int8_output, int8_time = generate_text(
        model_int8, tokenizer, test_input, max_length=50, device=device
    )
    print(f"INT8 model output: {int8_output}")
    print(f"INT8 model generation time: {int8_time:.3f} seconds")
    print(f"Speed change: {((int8_time - original_time) / original_time * 100):+.1f}%")
    
    # Create quantized model (INT4)
    print("\n=== Quantizing Model to INT4 ===")
    model_int4 = GPT2LMHeadModel.from_pretrained('gpt2')
    
    # Quantize to INT4
    quantize(model_int4, weights=qint4, activations=qint8)  # INT4 weights, INT8 activations
    freeze(model_int4)
    
    model_int4.to(device)
    model_int4.eval()
    
    # Measure INT4 model
    int4_model_size = get_model_size(model_int4)
    print(f"INT4 quantized model size: {int4_model_size:.2f} MB")
    print(f"Size reduction: {((original_model_size - int4_model_size) / original_model_size * 100):.1f}%")
    
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    memory_with_int4 = get_memory_usage()
    print(f"Memory usage with INT4 model: {memory_with_int4:.2f} MB")
    
    # Test INT4 model inference
    int4_output, int4_time = generate_text(
        model_int4, tokenizer, test_input, max_length=50, device=device
    )
    print(f"INT4 model output: {int4_output}")
    print(f"INT4 model generation time: {int4_time:.3f} seconds")
    print(f"Speed change: {((int4_time - original_time) / original_time * 100):+.1f}%")
    
    # Summary
    print("\n=== Summary ===")
    print(f"{'Model':<15} {'Size (MB)':<12} {'Memory (MB)':<12} {'Time (s)':<10} {'Size Reduction':<15}")
    print("-" * 75)
    print(f"{'Original':<15} {original_model_size:<12.2f} {memory_before_original:<12.2f} {original_time:<10.3f} {'0.0%':<15}")
    print(f"{'INT8':<15} {int8_model_size:<12.2f} {memory_with_int8:<12.2f} {int8_time:<10.3f} {((original_model_size - int8_model_size) / original_model_size * 100):<15.1f}%")
    print(f"{'INT4':<15} {int4_model_size:<12.2f} {memory_with_int4:<12.2f} {int4_time:<10.3f} {((original_model_size - int4_model_size) / original_model_size * 100):<15.1f}%")


if __name__ == "__main__":
    main()
