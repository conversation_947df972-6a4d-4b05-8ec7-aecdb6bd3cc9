#!/usr/bin/env python3
"""
Simple GPT-2 Quantization Example
"""

import torch
import time
from transformers import GPT2LMHeadModel, GPT2Tokenizer
from quanto import quantize, freeze, qint8


def get_model_memory_size(model):
    """Calculate model memory size in MB"""
    total_size = 0
    for param in model.parameters():
        total_size += param.nelement() * param.element_size()
    return total_size / (1024 * 1024)


def test_generation(model, tokenizer, input_text="The future of AI is", max_length=30):
    """Test text generation with timing"""
    # Encode input
    input_ids = tokenizer.encode(input_text, return_tensors='pt')
    
    # Generate with timing
    start_time = time.time()
    with torch.no_grad():
        outputs = model.generate(
            input_ids,
            max_length=max_length,
            do_sample=True,
            temperature=0.7,
            pad_token_id=tokenizer.eos_token_id
        )
    end_time = time.time()
    
    # Decode output
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    generation_time = end_time - start_time
    
    return generated_text, generation_time


def main():
    print("GPT-2 Quantization with Quanto\n")
    
    # Load model and tokenizer
    print("Loading GPT-2...")
    tokenizer = GPT2Tokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token
    
    # Original model
    model_fp32 = GPT2LMHeadModel.from_pretrained('gpt2')
    model_fp32.eval()
    
    print("=== Original FP32 Model ===")
    fp32_size = get_model_memory_size(model_fp32)
    print(f"Model size: {fp32_size:.2f} MB")
    
    # Test original model
    fp32_text, fp32_time = test_generation(model_fp32, tokenizer)
    print(f"Generated text: {fp32_text}")
    print(f"Generation time: {fp32_time:.3f} seconds\n")
    
    # Quantized model
    print("=== Quantizing to INT8 ===")
    model_int8 = GPT2LMHeadModel.from_pretrained('gpt2')
    
    # Apply quantization
    quantize(model_int8, weights=qint8, activations=qint8)
    freeze(model_int8)
    model_int8.eval()
    
    int8_size = get_model_memory_size(model_int8)
    print(f"Quantized model size: {int8_size:.2f} MB")
    print(f"Size reduction: {((fp32_size - int8_size) / fp32_size * 100):.1f}%")
    
    # Test quantized model
    int8_text, int8_time = test_generation(model_int8, tokenizer)
    print(f"Generated text: {int8_text}")
    print(f"Generation time: {int8_time:.3f} seconds")
    print(f"Speed change: {((int8_time - fp32_time) / fp32_time * 100):+.1f}%")


if __name__ == "__main__":
    main()
