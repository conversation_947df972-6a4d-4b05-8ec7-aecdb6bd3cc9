# GPT-2 Model Quantization with Quanto

This project demonstrates how to use the `quanto` library to quantize GPT-2 models and compare their memory usage and inference performance.

## Files

- `gpt2_quantization_demo.py` - Complete demo with detailed comparisons (FP32, INT8, INT4)
- `simple_quantization.py` - Simplified version focusing on core functionality
- `requirements.txt` - Required Python packages

## Installation

1. Install the required packages:
```bash
pip install -r requirements.txt
```

## Usage

### Run the complete demo:
```bash
python gpt2_quantization_demo.py
```

### Run the simple version:
```bash
python simple_quantization.py
```

## What the scripts do:

1. **Load GPT-2 model and tokenizer** - Uses the standard GPT-2 model from Hugging Face
2. **Measure original model** - Calculate memory usage and model size
3. **Apply quantization** - Use quanto to quantize weights and activations
4. **Compare performance** - Test generation speed and quality
5. **Show results** - Display memory savings and performance changes

## Key Features:

- ✅ Uses GPT-2 model as requested
- ✅ Direct token ID input (no pipeline)
- ✅ Uses `model.generate()` method
- ✅ Measures memory usage before/after quantization
- ✅ Compares inference performance
- ✅ Shows quantization effects on text generation

## Expected Results:

- **Memory reduction**: ~50-75% with INT8 quantization
- **Speed**: May vary depending on hardware
- **Quality**: Minimal impact on text generation quality

## Requirements:

- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- Quanto 0.1.0+
- psutil (for memory monitoring)
